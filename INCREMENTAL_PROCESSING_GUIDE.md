# Incremental Processing Guide for Batch-processing-Compact-v2.6.py

## Overview

The ImageJ/FIJI batch processing script now includes robust incremental processing capabilities that allow users to add new images to a folder and re-run the script without reprocessing existing images. This saves significant time and ensures data consistency.

## How Incremental Processing Works

### 1. **Duplicate Detection**
The script uses multiple methods to identify already processed images:

- **Primary Method**: Checks existing CSV file for image names
- **Backup Method**: Checks for processed PNG output files
- **Unicode Handling**: Normalizes filenames (µ → u) for consistent comparison

### 2. **Processing Logic**
When the script runs, it:

1. **Scans the directory** for all image files matching the specified file types
2. **Loads existing CSV data** from the parent directory (`particle_dual_predictions.csv`)
3. **Filters images** into two categories:
   - `images_already_processed`: Found in CSV or have PNG outputs
   - `images_to_process`: New images that need analysis
4. **Processes only new images** through the full particle analysis workflow
5. **Preserves existing data** and appends new results

### 3. **Data Consolidation**
The final output includes:

- **Complete CSV**: All images (existing + new) with consolidated statistics
- **Updated Excel file**: Formatted with all data including pore analysis
- **Recalculated totals**: Statistics reflect the complete dataset
- **Regenerated histogram**: Pore size distribution includes all data

## Key Features

### ✅ **Skip Already Processed Images**
- Images found in existing CSV are automatically skipped
- PNG output files are also checked as backup verification
- Clear logging shows which images are skipped vs. processed

### ✅ **Process Only New Images**
- Only images not found in existing data undergo full analysis
- Significant time savings for large datasets
- Maintains processing quality and accuracy

### ✅ **Preserve Existing Data**
- Previously processed results remain unchanged in CSV
- Existing pore analysis data is loaded from Excel files
- No data loss or corruption during incremental updates

### ✅ **Consolidate All Results**
- Final CSV contains both existing and new image results
- Each image appears exactly once in the final output
- Totals and statistics reflect the complete consolidated dataset
- Excel file includes complete pore analysis with updated histogram

## Usage Example

### Initial Run
```
Directory: C:/Images/
Files: image1.tif, image2.tif, image3.tif

Result: All 3 images processed, CSV created with 3 entries
```

### Incremental Run (Add New Images)
```
Directory: C:/Images/
Files: image1.tif, image2.tif, image3.tif, image4.tif, image5.tif

Script Output:
- Images already processed: 3 (will be skipped)
- Images to process: 2
- INCREMENTAL PROCESSING MODE: Adding 2 new images to existing 3 processed images

Result: Only image4.tif and image5.tif are processed, CSV updated with 5 total entries
```

## Log Output Example

```
============================================================
FIJI/ImageJ Compact Batch Particle Classification v2.6
============================================================
Found 5 image files in directory
Loaded existing CSV: 3 images already processed (156 particles)
Images already processed: 3 (will be skipped)
  -> Skipped images: image1.tif, image2.tif, image3.tif
Images to process: 2
INCREMENTAL PROCESSING MODE: Adding 2 new images to existing 3 processed images

PROCESSING: image4.tif
  -> Processed successfully
  -> Found 12 pores
PROCESSING: image5.tif
  -> Processed successfully
  -> Found 8 pores

CSV integrity check: 5 total images in final CSV (expected: 5)
CSV and Excel files updated successfully with 2 new images
```

## Technical Implementation

### Duplicate Prevention
- **CSV-based tracking**: Primary method using normalized filenames
- **File-based verification**: Checks for existing PNG outputs
- **Unicode normalization**: Handles special characters consistently
- **Duplicate removal**: Prevents duplicate entries within CSV

### Pore Data Handling
- **Existing pore data loading**: Reads pore measurements from existing Excel files
- **Incremental pore extraction**: Only extracts pores from newly processed images
- **Complete histogram generation**: Includes all pore data in final analysis

### Error Handling
- **Graceful degradation**: Script continues if some components fail
- **Data integrity checks**: Verifies final CSV contains expected number of images
- **Comprehensive logging**: Clear feedback on processing status

## Benefits

1. **Time Efficiency**: Avoid reprocessing hundreds of images when adding just a few new ones
2. **Data Consistency**: Existing results remain unchanged and verified
3. **Workflow Flexibility**: Add images to folders at any time and update results
4. **Resource Conservation**: Reduces computational load and processing time
5. **User-Friendly**: Clear logging shows exactly what's happening during processing

## File Structure

```
Project Directory/
├── Images/                          # Input images directory
│   ├── image1.tif                  # Already processed
│   ├── image2.tif                  # Already processed  
│   ├── image3.tif                  # Already processed
│   ├── image4.tif                  # New image (will be processed)
│   └── image5.tif                  # New image (will be processed)
└── particle_dual_predictions.csv   # Consolidated results (all images)
└── particle_dual_predictions.xlsx  # Formatted Excel with pore analysis
```

## Verification

The incremental processing logic has been thoroughly tested with:
- ✅ CSV loading and parsing
- ✅ Duplicate detection and handling
- ✅ Unicode filename normalization
- ✅ Image filtering logic
- ✅ Data consolidation accuracy

This ensures reliable operation across different scenarios and datasets.
