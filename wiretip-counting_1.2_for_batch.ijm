image_title = getArgument();
selectImage(image_title);

// Calibration
pixels = 604;
nanometers = 1000;
nm_per_pixel = nanometers/pixels;
run("Set Scale...", "distance="+pixels+" known="+nanometers+" unit=nm");
run("Set Measurements...", "area");

// Retrieving image properties
width = getWidth();
height = getHeight();
getPixelSize(unit, pixelWidth, pixelHeight);
physical_width = width*pixelWidth;
physical_height = height*pixelHeight;
physical_area = physical_height*physical_width;

// Image processing
run("32-bit");
run("Enhance Local Contrast (CLAHE)", "blocksize=127 histogram=256 maximum=3 mask=*None*");
setOption("ScaleConversions", true);
run("16-bit");
run("Exp");
run("Auto Threshold", "method=Default white");
run("Analyze Particles...", "size="+25*nm_per_pixel*nm_per_pixel+"-Infinity display clear include add record");

// Printing results
n = nResults;

run("Clear Results");
updateResults();

setResult("Tip Count", 0, n);
setResult("Area [um^2]", 0, physical_area / 1000000);
setResult("Tips per um^2", 0, n / physical_area * 1000000);
updateResults();


