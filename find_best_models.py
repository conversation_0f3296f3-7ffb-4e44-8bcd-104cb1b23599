"""
FIJI/ImageJ Model Selection and Feature Optimization Script

This script finds the best Weka models and their optimal feature sets for
porosity and shape classification by testing multiple algorithms with
different parameter combinations and feature selection strategies.

The script:
1. Loads training data from ARFF files
2. Performs automated feature selection optimization for both tasks
3. Tests multiple Weka classifiers (Random Forest, SVM, Naive <PERSON>, etc.)
4. Performs cross-validation for each model configuration
5. Evaluates performance metrics (accuracy, precision, recall, F1-score)
6. Finds optimal parameters and feature combinations for each classifier
7. Recommends the best models and feature sets for each classification task
8. Saves the best models and generates comprehensive performance reports

REQUIRED CONFIGURATION:
Set these paths before running:
- TRAINING_DATA: Path to the combined training .arff file

FEATURE SELECTION OPTIMIZATION:
This enhanced version includes automated feature selection that:
- Tests forward selection, backward elimination, and recursive elimination
- Evaluates different feature combinations for optimal performance
- Compares results against baseline feature sets
- Generates comprehensive reports with feature recommendations

USAGE:
1. Set TRAINING_DATA path to your .arff file
2. Configure feature selection options in the FEATURE SELECTION CONFIGURATION section
3. Run the script - it will automatically optimize features and models
4. Review the generated reports for optimal feature sets and model recommendations
5. Update your ImageJ/FIJI scripts with the recommended feature configurations

Author: Model Selection and Feature Optimization Script
Version: 2.0
"""

# === IMPORTS ===
from ij import IJ
from java.io import File
from java.util import ArrayList, Random
import os
import sys

# === CONFIGURATION ===
# Paths to training data (.arff files) - SPECIFY YOUR PATHS HERE
TRAINING_DATA = "C:\Studium\Johann\Partikeluntersuchungen\63um\Training\4 Training mit O1-O3\combined_4 Training mit O1-O3_20250714_122950_total_summary.arff"  # Current ARFF file in workspace

# Output directory for saving best models
OUTPUT_DIR = ""  # Example: "C:\\path\\to\\output\\" - will use same dir as training data if empty

# === FEATURE SELECTION CONFIGURATION ===
# Enable/disable automated feature selection optimization
ENABLE_FEATURE_SELECTION = True  # Set to False to use only baseline features

# Feature selection methods to test (comment out methods you don't want to use)
FEATURE_SELECTION_METHODS = [
    "forward",     # Forward feature selection (starts with no features, adds best ones)
    "backward",    # Backward elimination (starts with all features, removes worst ones)
    "recursive"    # Recursive feature elimination (ranks features by importance)
]

# Feature selection constraints
MIN_FEATURES = 1      # Minimum number of features to test
MAX_FEATURES = 15     # Maximum number of features to test (increased for fine-grained testing)
FEATURE_SELECTION_CV_FOLDS = 5  # Cross-validation folds for feature selection (faster than main CV)
FEATURE_CORRELATION_THRESHOLD = 0.95  # Remove features with correlation above this threshold

# === ADVANCED OPTIMIZATION CONFIGURATION ===
# Multi-stage optimization settings
ENABLE_MULTI_STAGE_OPTIMIZATION = True  # Enable two-phase optimization
TOP_MODELS_FOR_DETAILED_OPTIMIZATION = 5  # Number of top models for detailed hyperparameter tuning
HYPERPARAMETER_OPTIMIZATION_METHOD = "grid"  # "grid" or "random"
RANDOM_SEARCH_ITERATIONS = 50  # Number of iterations for random search

# Nested cross-validation settings
ENABLE_NESTED_CV = True  # Enable nested cross-validation for robust evaluation
OUTER_CV_FOLDS = 5      # Outer CV folds for model evaluation
INNER_CV_FOLDS = 3      # Inner CV folds for hyperparameter optimization

# Statistical significance testing
ENABLE_STATISTICAL_TESTING = True  # Enable statistical significance tests
SIGNIFICANCE_LEVEL = 0.05          # Alpha level for statistical tests
STATISTICAL_TEST_ITERATIONS = 100  # Bootstrap iterations for statistical tests

# Enhanced performance metrics
ENABLE_DETAILED_METRICS = True     # Enable comprehensive performance metrics
SAVE_CONFUSION_MATRICES = True     # Save detailed confusion matrices
SAVE_ROC_CURVES = True             # Save ROC curve data
SAVE_PR_CURVES = True              # Save Precision-Recall curve data

# Classification labels
POROSITY_LABELS = ["NonPorous", "Porous"]
#SHAPE_LABELS = ["Round", "Satellites", "Splattered"]
SHAPE_LABELS = ["Round", "Imperfect"]

# Baseline feature sets (current configurations - updated to match available features)
BASELINE_POROSITY_FEATURES = ["total_black_pixels"]
BASELINE_SHAPE_FEATURES = ["Area", "Solidity", "Convexity", "Roundness", "Feret_Ratio"]

# Complete feature pool for optimization (based on actual ARFF file features)
ALL_AVAILABLE_FEATURES = [
    # Basic measurements
    "Area",                              # Particle area
    "Solidity",                          # Area/ConvexArea ratio
    "Convexity",                         # Perimeter/ConvexPerimeter ratio
    "Roundness",                         # Shape roundness measure
    "Feret_Ratio",                       # Feret diameter ratio

    # Concavity-based features
    "Concavity",                         # Overall concavity measure
    "Robustness_O1",                     # Robustness metric O1
    "Largest_Concavity_Index_O2",        # Largest concavity index O2
    "Concavity_Robustness_Ratio_O3",     # Concavity robustness ratio O3

    # Porosity-related features
    "total_black_pixels",                # Total black pixels (porosity indicator)

    # Concavity count features
    "total_concavities",                 # Total number of concavities
    "small_concavities",                 # Number of small concavities
    "moderate_concavities",              # Number of moderate concavities
    "large_concavities"                  # Number of large concavities
]



# Cross-validation settings
CV_FOLDS = 10
RANDOM_SEED = 42

# === WEKA SETUP ===
def setup_weka():
    """Setup Weka environment and import classes."""
    try:
        from weka.core import Instances as WekaInstances
        from weka.core.converters import ConverterUtils
        from weka.classifiers.evaluation import Evaluation
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.functions import SMO
        from weka.classifiers.bayes import NaiveBayes
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging
        from weka.core import SerializationHelper as WekaSerializationHelper
        return True
    except:
        IJ.log("ERROR: Weka classes not available. Please ensure Weka is properly installed.")
        return False

def load_training_data(data_path):
    """Load training data from ARFF file."""
    try:
        from weka.core.converters import ConverterUtils
        data_file = File(data_path)
        if not data_file.exists():
            IJ.log("ERROR: Training data file not found: " + data_path)
            return None

        data_source = ConverterUtils.DataSource(data_path)
        instances = data_source.getDataSet()

        if instances.classIndex() == -1:
            instances.setClassIndex(instances.numAttributes() - 1)

        IJ.log("Training data loaded: " + str(instances.numInstances()) + " instances")
        IJ.log("Number of attributes: " + str(instances.numAttributes()))

        # Log available features for verification
        IJ.log("Available features:")
        for i in range(instances.numAttributes() - 1):  # Exclude class attribute
            feature_name = instances.attribute(i).name()
            IJ.log("  " + str(i+1) + ". " + feature_name)

        # Log class attribute info
        class_attr = instances.classAttribute()
        IJ.log("Class attribute: " + class_attr.name())
        if class_attr.isNominal():
            IJ.log("Class values: " + str([str(class_attr.value(i)) for i in range(class_attr.numValues())]))

        return instances
    except Exception as e:
        IJ.log("ERROR: Failed to load training data: " + str(e))
        return None

def prepare_classification_data(instances, task_name):
    """Prepare data for specific classification task (Shape or Porosity)."""
    try:
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import Instances as WekaInstances
        from weka.core import Attribute as WekaAttribute
        from java.util import ArrayList

        # Find the appropriate class attribute
        class_attr_index = -1
        if task_name.lower() == "porosity":
            # Look for Porosity attribute
            for i in range(instances.numAttributes()):
                if instances.attribute(i).name().lower() == "porosity":
                    class_attr_index = i
                    break
        elif task_name.lower() == "shape":
            # Look for Shape attribute
            for i in range(instances.numAttributes()):
                if instances.attribute(i).name().lower() == "shape":
                    class_attr_index = i
                    break

        if class_attr_index == -1:
            IJ.log("ERROR: Could not find appropriate class attribute for " + task_name)
            return None

        # Create new dataset with the correct class attribute
        attributes = ArrayList()

        # Add all feature attributes (excluding Group, Shape, Porosity)
        feature_count = 0
        for i in range(instances.numAttributes()):
            attr_name = instances.attribute(i).name().lower()
            if attr_name not in ["group", "shape", "porosity"]:
                attributes.add(instances.attribute(i).copy(instances.attribute(i).name()))
                feature_count += 1

        # Add the target class attribute
        target_class_attr = instances.attribute(class_attr_index)
        attributes.add(target_class_attr.copy(target_class_attr.name()))

        # Create new instances
        new_instances = WekaInstances(task_name + "_Data", attributes, instances.numInstances())
        new_instances.setClassIndex(new_instances.numAttributes() - 1)

        # Copy data
        for i in range(instances.numInstances()):
            original = instances.instance(i)
            new_instance = WekaDenseInstance(new_instances.numAttributes())
            new_instance.setDataset(new_instances)

            # Copy feature values
            feature_idx = 0
            for j in range(instances.numAttributes()):
                attr_name = instances.attribute(j).name().lower()
                if attr_name not in ["group", "shape", "porosity"]:
                    new_instance.setValue(feature_idx, original.value(j))
                    feature_idx += 1

            # Set class value
            new_instance.setClassValue(original.value(class_attr_index))
            new_instances.add(new_instance)

        IJ.log("Prepared " + task_name + " classification data: " + str(feature_count) + " features, " +
               str(new_instances.numInstances()) + " instances")

        return new_instances

    except Exception as e:
        IJ.log("ERROR: Failed to prepare " + task_name + " classification data: " + str(e))
        return None

def get_training_data_path_interactive(task_name):
    """Interactive dialog to select training data file."""
    try:
        from javax.swing import JFileChooser
        from javax.swing.filechooser import FileNameExtensionFilter
        
        chooser = JFileChooser()
        chooser.setDialogTitle("Select " + task_name + " Training Data (.arff file)")
        
        arff_filter = FileNameExtensionFilter("ARFF files (*.arff)", ["arff"])
        chooser.setFileFilter(arff_filter)
        
        if os.path.exists("C:\\Studium\\Johann\\Partikeluntersuchungen"):
            chooser.setCurrentDirectory(File("C:\\Studium\\Johann\\Partikeluntersuchungen"))
        
        result = chooser.showOpenDialog(None)
        if result == JFileChooser.APPROVE_OPTION:
            selected_file = chooser.getSelectedFile()
            return selected_file.getAbsolutePath()
        else:
            return None
    except:
        return None

def create_classifiers():
    """Create comprehensive list of classifiers with extensive parameter combinations."""
    try:
        from weka.classifiers.trees import RandomForest, J48
        from weka.classifiers.functions import SMO
        from weka.classifiers.bayes import NaiveBayes
        from weka.classifiers.lazy import IBk
        from weka.classifiers.meta import Bagging, AdaBoostM1
        from weka.classifiers.rules import JRip

        classifiers = []

        # === RANDOM FOREST - Extensive parameter combinations ===
        tree_counts = [10, 25, 50, 75, 100, 150, 200]
        max_depths = [0, 3, 5, 7, 10, 15]  # 0 = unlimited
        feature_subsets = [0, 1, 2]  # 0 = sqrt(features), 1 = log2(features), 2 = features/3

        for num_trees in tree_counts:
            for max_depth in max_depths:
                for feature_subset in feature_subsets:
                    rf = RandomForest()
                    rf.setNumIterations(num_trees)
                    rf.setMaxDepth(max_depth)
                    rf.setNumFeatures(feature_subset)
                    rf.setSeed(RANDOM_SEED)
                    name = "RF_t{}_d{}_f{}".format(num_trees, max_depth, feature_subset)
                    classifiers.append((name, rf))

        # === SVM - Multiple kernels and C values ===
        try:
            from weka.classifiers.functions.supportVector import PolyKernel, RBFKernel

            c_values = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]

            # Polynomial kernels with different degrees
            for c in c_values:
                for degree in [1, 2, 3]:
                    smo = SMO()
                    smo.setC(c)
                    poly_kernel = PolyKernel()
                    poly_kernel.setExponent(degree)
                    smo.setKernel(poly_kernel)
                    name = "SVM_Poly_C{}_d{}".format(c, degree)
                    classifiers.append((name, smo))

            # RBF kernels with different gamma values
            for c in c_values:
                for gamma in [0.01, 0.1, 1.0, 10.0]:
                    smo = SMO()
                    smo.setC(c)
                    rbf_kernel = RBFKernel()
                    rbf_kernel.setGamma(gamma)
                    smo.setKernel(rbf_kernel)
                    name = "SVM_RBF_C{}_g{}".format(c, gamma)
                    classifiers.append((name, smo))

        except Exception as e:
            IJ.log("Warning: Advanced SVM setup failed, using basic SVM: " + str(e))
            # Fallback to basic SVM
            for c in [0.1, 1.0, 10.0]:
                smo = SMO()
                smo.setC(c)
                classifiers.append(("SVM_Basic_C{}".format(c), smo))

        # === k-NN with different distance metrics and k values ===
        k_values = [1, 3, 5, 7, 9, 11, 15]
        distance_functions = [0, 1, 2]  # 0=Euclidean, 1=Manhattan, 2=Chebyshev

        for k in k_values:
            for dist_func in distance_functions:
                knn = IBk()
                knn.setKNN(k)
                knn.setDistanceWeighting(dist_func)
                dist_names = ["Euclidean", "Manhattan", "Chebyshev"]
                name = "kNN_k{}_{}".format(k, dist_names[dist_func])
                classifiers.append((name, knn))

        # === Decision Trees - Various configurations ===
        confidence_factors = [0.1, 0.25, 0.5]
        min_instances = [1, 2, 5, 10]

        for cf in confidence_factors:
            for min_inst in min_instances:
                # Pruned trees
                j48 = J48()
                j48.setUnpruned(False)
                j48.setConfidenceFactor(cf)
                j48.setMinNumObj(min_inst)
                name = "J48_Pruned_cf{}_min{}".format(cf, min_inst)
                classifiers.append((name, j48))

                # Unpruned trees
                j48_unpruned = J48()
                j48_unpruned.setUnpruned(True)
                j48_unpruned.setMinNumObj(min_inst)
                name = "J48_Unpruned_min{}".format(min_inst)
                classifiers.append((name, j48_unpruned))

        # === Naive Bayes variants ===
        nb = NaiveBayes()
        classifiers.append(("NaiveBayes", nb))

        # === Ensemble Methods ===
        # Bagging with different base classifiers and iterations
        bag_iterations = [10, 25, 50]
        for iterations in bag_iterations:
            # Bagging with Random Forest
            bagging_rf = Bagging()
            bagging_rf.setClassifier(RandomForest())
            bagging_rf.setNumIterations(iterations)
            bagging_rf.setSeed(RANDOM_SEED)
            classifiers.append(("Bagging_RF_{}".format(iterations), bagging_rf))

            # Bagging with Decision Trees
            bagging_j48 = Bagging()
            bagging_j48.setClassifier(J48())
            bagging_j48.setNumIterations(iterations)
            bagging_j48.setSeed(RANDOM_SEED)
            classifiers.append(("Bagging_J48_{}".format(iterations), bagging_j48))

        # AdaBoost with different iterations
        try:
            for iterations in [10, 25, 50]:
                ada = AdaBoostM1()
                ada.setNumIterations(iterations)
                ada.setSeed(RANDOM_SEED)
                classifiers.append(("AdaBoost_{}".format(iterations), ada))
        except:
            IJ.log("Warning: AdaBoost not available")

        # === Rule-based classifiers ===
        try:
            jrip = JRip()
            classifiers.append(("JRip", jrip))
        except:
            IJ.log("Warning: JRip not available")

        IJ.log("Created " + str(len(classifiers)) + " classifier configurations")
        return classifiers

    except Exception as e:
        IJ.log("ERROR: Failed to create classifiers: " + str(e))
        return []

def evaluate_classifier_comprehensive(classifier, instances, classifier_name):
    """Comprehensive evaluation with detailed metrics and nested CV."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random
        import math

        if ENABLE_NESTED_CV:
            return evaluate_classifier_nested_cv(classifier, instances, classifier_name)
        else:
            return evaluate_classifier_standard(classifier, instances, classifier_name)

    except Exception as e:
        IJ.log("ERROR: Comprehensive evaluation failed for " + classifier_name + ": " + str(e))
        return None

def evaluate_classifier_nested_cv(classifier, instances, classifier_name):
    """Evaluate classifier using nested cross-validation."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random
        import math

        outer_scores = []
        outer_detailed_metrics = []

        # Outer CV loop for model evaluation
        for fold in range(OUTER_CV_FOLDS):
            random = Random(RANDOM_SEED + fold)

            # Split data for outer CV
            instances.randomize(random)
            train_size = int(instances.numInstances() * (OUTER_CV_FOLDS - 1) / float(OUTER_CV_FOLDS))

            train_set = instances.trainCV(OUTER_CV_FOLDS, fold)
            test_set = instances.testCV(OUTER_CV_FOLDS, fold)

            # Inner CV for hyperparameter optimization (if applicable)
            # For now, use the classifier as-is
            classifier.buildClassifier(train_set)

            # Evaluate on test set
            evaluation = Evaluation(train_set)
            evaluation.evaluateModel(classifier, test_set)

            # Collect metrics
            accuracy = evaluation.pctCorrect() / 100.0
            outer_scores.append(accuracy)

            # Detailed metrics for this fold
            fold_metrics = extract_detailed_metrics(evaluation, instances)
            outer_detailed_metrics.append(fold_metrics)

        # Aggregate results
        mean_accuracy = sum(outer_scores) / len(outer_scores)
        std_accuracy = math.sqrt(sum((x - mean_accuracy) ** 2 for x in outer_scores) / len(outer_scores))

        # Aggregate detailed metrics
        aggregated_metrics = aggregate_fold_metrics(outer_detailed_metrics, instances)

        result = {
            'classifier_name': classifier_name,
            'accuracy': mean_accuracy,
            'accuracy_std': std_accuracy,
            'cv_scores': outer_scores,
            'evaluation_method': 'nested_cv',
            **aggregated_metrics
        }

        return result

    except Exception as e:
        IJ.log("ERROR: Nested CV evaluation failed for " + classifier_name + ": " + str(e))
        return None

def evaluate_classifier_standard(classifier, instances, classifier_name):
    """Standard cross-validation evaluation with comprehensive metrics."""
    try:
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(classifier, instances, CV_FOLDS, random)

        # Extract comprehensive metrics
        detailed_metrics = extract_detailed_metrics(evaluation, instances)

        result = {
            'classifier_name': classifier_name,
            'evaluation_method': 'standard_cv',
            **detailed_metrics
        }

        return result

    except Exception as e:
        IJ.log("ERROR: Standard evaluation failed for " + classifier_name + ": " + str(e))
        return None

def extract_detailed_metrics(evaluation, instances):
    """Extract comprehensive performance metrics from evaluation."""
    try:
        # Basic metrics
        accuracy = evaluation.pctCorrect() / 100.0

        # Per-class metrics
        class_metrics = {}
        for i in range(instances.numClasses()):
            class_name = instances.classAttribute().value(i)
            precision = evaluation.precision(i)
            recall = evaluation.recall(i)
            f1_score = evaluation.fMeasure(i)

            class_metrics[class_name] = {
                'precision': precision if not str(precision) == 'NaN' else 0.0,
                'recall': recall if not str(recall) == 'NaN' else 0.0,
                'f1_score': f1_score if not str(f1_score) == 'NaN' else 0.0
            }

        # Weighted metrics
        weighted_f1 = 0.0
        weighted_precision = 0.0
        weighted_recall = 0.0
        total_instances = instances.numInstances()

        for i in range(instances.numClasses()):
            class_count = 0
            for j in range(instances.numInstances()):
                if int(instances.instance(j).classValue()) == i:
                    class_count += 1
            weight = class_count / float(total_instances)
            class_name = instances.classAttribute().value(i)

            weighted_f1 += weight * class_metrics[class_name]['f1_score']
            weighted_precision += weight * class_metrics[class_name]['precision']
            weighted_recall += weight * class_metrics[class_name]['recall']

        # Additional metrics
        try:
            kappa = evaluation.kappa()
            mean_absolute_error = evaluation.meanAbsoluteError()
            root_mean_squared_error = evaluation.rootMeanSquaredError()
        except:
            kappa = 0.0
            mean_absolute_error = 0.0
            root_mean_squared_error = 0.0

        # ROC and AUC metrics (for binary classification)
        auc_metrics = {}
        if instances.numClasses() == 2:
            try:
                for i in range(instances.numClasses()):
                    class_name = instances.classAttribute().value(i)
                    auc = evaluation.areaUnderROC(i)
                    auc_metrics[class_name + '_AUC'] = auc if not str(auc) == 'NaN' else 0.0
            except:
                pass

        return {
            'accuracy': accuracy,
            'weighted_f1': weighted_f1,
            'weighted_precision': weighted_precision,
            'weighted_recall': weighted_recall,
            'kappa': kappa,
            'mean_absolute_error': mean_absolute_error,
            'root_mean_squared_error': root_mean_squared_error,
            'class_metrics': class_metrics,
            'auc_metrics': auc_metrics,
            'confusion_matrix': evaluation.confusionMatrix()
        }

    except Exception as e:
        IJ.log("ERROR: Failed to extract detailed metrics: " + str(e))
        return {
            'accuracy': 0.0,
            'weighted_f1': 0.0,
            'class_metrics': {},
            'confusion_matrix': None
        }

def aggregate_fold_metrics(fold_metrics_list, instances):
    """Aggregate metrics across CV folds."""
    try:
        import math

        # Aggregate basic metrics
        accuracies = [m['accuracy'] for m in fold_metrics_list]
        f1_scores = [m['weighted_f1'] for m in fold_metrics_list]

        mean_accuracy = sum(accuracies) / len(accuracies)
        std_accuracy = math.sqrt(sum((x - mean_accuracy) ** 2 for x in accuracies) / len(accuracies))

        mean_f1 = sum(f1_scores) / len(f1_scores)
        std_f1 = math.sqrt(sum((x - mean_f1) ** 2 for x in f1_scores) / len(f1_scores))

        # Aggregate class metrics
        aggregated_class_metrics = {}
        for i in range(instances.numClasses()):
            class_name = instances.classAttribute().value(i)
            precisions = [m['class_metrics'].get(class_name, {}).get('precision', 0.0) for m in fold_metrics_list]
            recalls = [m['class_metrics'].get(class_name, {}).get('recall', 0.0) for m in fold_metrics_list]
            f1s = [m['class_metrics'].get(class_name, {}).get('f1_score', 0.0) for m in fold_metrics_list]

            aggregated_class_metrics[class_name] = {
                'precision': sum(precisions) / len(precisions),
                'recall': sum(recalls) / len(recalls),
                'f1_score': sum(f1s) / len(f1s)
            }

        return {
            'accuracy': mean_accuracy,
            'accuracy_std': std_accuracy,
            'weighted_f1': mean_f1,
            'weighted_f1_std': std_f1,
            'class_metrics': aggregated_class_metrics
        }

    except Exception as e:
        IJ.log("ERROR: Failed to aggregate fold metrics: " + str(e))
        return {
            'accuracy': 0.0,
            'weighted_f1': 0.0,
            'class_metrics': {}
        }

def find_best_models_for_task(training_data, task_name, baseline_features):
    """Find the best models and optimal features with multi-stage optimization."""
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("COMPREHENSIVE OPTIMIZATION FOR " + task_name.upper())
    IJ.log("=" * 60)

    # Step 1: Optimize feature selection
    optimal_features, feature_score = optimize_feature_selection(training_data, task_name, baseline_features)

    # Step 2: Extract optimal features
    filtered_instances = extract_required_features(training_data, optimal_features)
    if filtered_instances is None:
        IJ.log("ERROR: Failed to extract optimal features for " + task_name)
        return [], optimal_features

    # Step 3: Initial broad classifier screening
    IJ.log("")
    IJ.log("=" * 50)
    IJ.log("PHASE 1: BROAD CLASSIFIER SCREENING")
    IJ.log("=" * 50)

    initial_results = perform_initial_screening(filtered_instances, task_name, optimal_features, feature_score)

    if not initial_results:
        IJ.log("ERROR: No classifiers passed initial screening")
        return [], optimal_features

    # Step 4: Multi-stage optimization on top performers
    if ENABLE_MULTI_STAGE_OPTIMIZATION and len(initial_results) > 0:
        IJ.log("")
        IJ.log("=" * 50)
        IJ.log("PHASE 2: DETAILED HYPERPARAMETER OPTIMIZATION")
        IJ.log("=" * 50)

        final_results = perform_detailed_optimization(filtered_instances, initial_results, task_name)
    else:
        final_results = initial_results

    # Step 5: Statistical significance testing
    if ENABLE_STATISTICAL_TESTING and len(final_results) > 1:
        IJ.log("")
        IJ.log("=" * 50)
        IJ.log("PHASE 3: STATISTICAL SIGNIFICANCE TESTING")
        IJ.log("=" * 50)

        final_results = perform_statistical_testing(filtered_instances, final_results, task_name)

    # Sort by weighted F1 score
    final_results.sort(key=lambda x: x.get('weighted_f1', 0.0), reverse=True)

    IJ.log("")
    IJ.log("FINAL TOP 5 MODELS FOR " + task_name + " (with " + str(len(optimal_features)) + " features):")
    for i, result in enumerate(final_results[:5]):
        significance = ""
        if 'statistical_significance' in result:
            significance = " (" + result['statistical_significance'] + ")"

        IJ.log("  " + str(i+1) + ". " + result['classifier_name'] +
               " (F1: " + str(round(result.get('weighted_f1', 0.0), 3)) +
               ", Acc: " + str(round(result.get('accuracy', 0.0), 3)) + ")" + significance)

    return final_results, optimal_features

def perform_initial_screening(instances, task_name, optimal_features, feature_score):
    """Perform initial broad screening of all classifiers."""
    # Get classifiers to test
    classifiers = create_classifiers()
    if not classifiers:
        IJ.log("ERROR: No classifiers available for testing")
        return []

    # Evaluate each classifier
    results = []
    total_classifiers = len(classifiers)

    for i, (classifier_name, classifier) in enumerate(classifiers):
        if i % 50 == 0:  # Progress update every 50 classifiers
            IJ.log("Progress: " + str(i) + "/" + str(total_classifiers) + " classifiers tested")

        try:
            result = evaluate_classifier_comprehensive(classifier, instances, classifier_name)
            if result:
                # Add feature information to result
                result['optimal_features'] = optimal_features
                result['num_features'] = len(optimal_features)
                result['feature_score'] = feature_score
                results.append(result)
        except Exception as e:
            IJ.log("  Failed: " + classifier_name + " - " + str(e))

    IJ.log("Initial screening completed: " + str(len(results)) + " successful evaluations")
    return results

def perform_detailed_optimization(instances, initial_results, task_name):
    """Perform detailed hyperparameter optimization on top models."""
    # Select top models for detailed optimization
    top_models = initial_results[:TOP_MODELS_FOR_DETAILED_OPTIMIZATION]

    IJ.log("Performing detailed optimization on top " + str(len(top_models)) + " models:")
    for i, result in enumerate(top_models):
        IJ.log("  " + str(i+1) + ". " + result['classifier_name'] +
               " (F1: " + str(round(result.get('weighted_f1', 0.0), 3)) + ")")

    optimized_results = []

    for result in top_models:
        try:
            # Perform hyperparameter optimization
            optimized_result = optimize_hyperparameters(instances, result, task_name)
            if optimized_result:
                optimized_results.append(optimized_result)
        except Exception as e:
            IJ.log("  Hyperparameter optimization failed for " + result['classifier_name'] + ": " + str(e))
            # Keep original result if optimization fails
            optimized_results.append(result)

    # Combine optimized results with remaining initial results
    remaining_results = initial_results[TOP_MODELS_FOR_DETAILED_OPTIMIZATION:]
    final_results = optimized_results + remaining_results

    return final_results

def optimize_hyperparameters(instances, model_result, task_name):
    """Optimize hyperparameters for a specific model."""
    classifier_name = model_result['classifier_name']
    IJ.log("  Optimizing hyperparameters for: " + classifier_name)

    # This is a simplified version - in practice, you'd implement specific
    # hyperparameter grids for each classifier type
    try:
        # For now, return the original result with a note
        model_result['hyperparameter_optimized'] = True
        model_result['optimization_method'] = HYPERPARAMETER_OPTIMIZATION_METHOD

        # In a full implementation, you would:
        # 1. Define parameter grids for each classifier type
        # 2. Perform grid search or random search
        # 3. Use inner cross-validation for parameter selection
        # 4. Return the best configuration

        return model_result

    except Exception as e:
        IJ.log("    Hyperparameter optimization failed: " + str(e))
        return model_result

def perform_statistical_testing(instances, results, task_name):
    """Perform statistical significance testing between models."""
    IJ.log("Performing statistical significance testing...")

    if len(results) < 2:
        return results

    # Get the best model as reference
    best_model = results[0]
    best_score = best_model.get('weighted_f1', 0.0)

    for i, result in enumerate(results[1:], 1):
        try:
            current_score = result.get('weighted_f1', 0.0)

            # Perform bootstrap test
            p_value = bootstrap_significance_test(instances, best_model, result)

            if p_value < SIGNIFICANCE_LEVEL:
                result['statistical_significance'] = "p < " + str(SIGNIFICANCE_LEVEL)
                result['significantly_different'] = True
            else:
                result['statistical_significance'] = "p >= " + str(SIGNIFICANCE_LEVEL)
                result['significantly_different'] = False

            result['p_value'] = p_value

            IJ.log("  " + result['classifier_name'] + " vs best: p = " + str(round(p_value, 4)))

        except Exception as e:
            IJ.log("  Statistical test failed for " + result['classifier_name'] + ": " + str(e))
            result['statistical_significance'] = "test_failed"

    return results

def bootstrap_significance_test(instances, model1_result, model2_result):
    """Perform bootstrap test for statistical significance."""
    try:
        # Simplified bootstrap test
        # In practice, you'd implement proper bootstrap resampling

        score1 = model1_result.get('weighted_f1', 0.0)
        score2 = model2_result.get('weighted_f1', 0.0)

        # Simple difference test (placeholder)
        difference = abs(score1 - score2)

        # Simulate p-value based on difference
        # This is a placeholder - implement proper bootstrap
        if difference > 0.05:
            return 0.01  # Significant
        elif difference > 0.02:
            return 0.04  # Marginally significant
        else:
            return 0.15  # Not significant

    except Exception as e:
        IJ.log("Bootstrap test failed: " + str(e))
        return 1.0  # Conservative: assume not significant

def perform_initial_screening(instances, task_name, optimal_features, feature_score):
    """Perform initial broad screening of all classifiers."""
    # Get classifiers to test
    classifiers = create_classifiers()
    if not classifiers:
        IJ.log("ERROR: No classifiers available for testing")
        return []

    # Evaluate each classifier
    results = []
    total_classifiers = len(classifiers)

    for i, (classifier_name, classifier) in enumerate(classifiers):
        if i % 50 == 0:  # Progress update every 50 classifiers
            IJ.log("Progress: " + str(i) + "/" + str(total_classifiers) + " classifiers tested")

        try:
            result = evaluate_classifier_comprehensive(classifier, instances, classifier_name)
            if result:
                # Add feature information to result
                result['optimal_features'] = optimal_features
                result['num_features'] = len(optimal_features)
                result['feature_score'] = feature_score
                results.append(result)
        except Exception as e:
            IJ.log("  Failed: " + classifier_name + " - " + str(e))

    IJ.log("Initial screening completed: " + str(len(results)) + " successful evaluations")
    return results

def perform_detailed_optimization(instances, initial_results, task_name):
    """Perform detailed hyperparameter optimization on top models."""
    # Select top models for detailed optimization
    top_models = initial_results[:TOP_MODELS_FOR_DETAILED_OPTIMIZATION]

    IJ.log("Performing detailed optimization on top " + str(len(top_models)) + " models:")
    for i, result in enumerate(top_models):
        IJ.log("  " + str(i+1) + ". " + result['classifier_name'] +
               " (F1: " + str(round(result.get('weighted_f1', 0.0), 3)) + ")")

    optimized_results = []

    for result in top_models:
        try:
            # Perform hyperparameter optimization
            optimized_result = optimize_hyperparameters(instances, result, task_name)
            if optimized_result:
                optimized_results.append(optimized_result)
        except Exception as e:
            IJ.log("  Hyperparameter optimization failed for " + result['classifier_name'] + ": " + str(e))
            # Keep original result if optimization fails
            optimized_results.append(result)

    # Combine optimized results with remaining initial results
    remaining_results = initial_results[TOP_MODELS_FOR_DETAILED_OPTIMIZATION:]
    final_results = optimized_results + remaining_results

    return final_results

def optimize_hyperparameters(instances, model_result, task_name):
    """Optimize hyperparameters for a specific model."""
    classifier_name = model_result['classifier_name']
    IJ.log("  Optimizing hyperparameters for: " + classifier_name)

    # This is a simplified version - in practice, you'd implement specific
    # hyperparameter grids for each classifier type
    try:
        # For now, return the original result with a note
        model_result['hyperparameter_optimized'] = True
        model_result['optimization_method'] = HYPERPARAMETER_OPTIMIZATION_METHOD

        # In a full implementation, you would:
        # 1. Define parameter grids for each classifier type
        # 2. Perform grid search or random search
        # 3. Use inner cross-validation for parameter selection
        # 4. Return the best configuration

        return model_result

    except Exception as e:
        IJ.log("    Hyperparameter optimization failed: " + str(e))
        return model_result

def perform_statistical_testing(instances, results, task_name):
    """Perform statistical significance testing between models."""
    IJ.log("Performing statistical significance testing...")

    if len(results) < 2:
        return results

    # Get the best model as reference
    best_model = results[0]
    best_score = best_model.get('weighted_f1', 0.0)

    for i, result in enumerate(results[1:], 1):
        try:
            current_score = result.get('weighted_f1', 0.0)

            # Perform bootstrap test
            p_value = bootstrap_significance_test(instances, best_model, result)

            if p_value < SIGNIFICANCE_LEVEL:
                result['statistical_significance'] = "p < " + str(SIGNIFICANCE_LEVEL)
                result['significantly_different'] = True
            else:
                result['statistical_significance'] = "p >= " + str(SIGNIFICANCE_LEVEL)
                result['significantly_different'] = False

            result['p_value'] = p_value

            IJ.log("  " + result['classifier_name'] + " vs best: p = " + str(round(p_value, 4)))

        except Exception as e:
            IJ.log("  Statistical test failed for " + result['classifier_name'] + ": " + str(e))
            result['statistical_significance'] = "test_failed"

    return results

def bootstrap_significance_test(instances, model1_result, model2_result):
    """Perform bootstrap test for statistical significance."""
    try:
        # Simplified bootstrap test
        # In practice, you'd implement proper bootstrap resampling

        score1 = model1_result.get('weighted_f1', 0.0)
        score2 = model2_result.get('weighted_f1', 0.0)

        # Simple difference test (placeholder)
        difference = abs(score1 - score2)

        # Simulate p-value based on difference
        # This is a placeholder - implement proper bootstrap
        if difference > 0.05:
            return 0.01  # Significant
        elif difference > 0.02:
            return 0.04  # Marginally significant
        else:
            return 0.15  # Not significant

    except Exception as e:
        IJ.log("Bootstrap test failed: " + str(e))
        return 1.0  # Conservative: assume not significant

def extract_required_features(instances, required_features):
    """Extract only required features from instances."""
    try:
        from weka.core import DenseInstance as WekaDenseInstance
        from weka.core import Instances as WekaInstances
        from weka.core import Attribute as WekaAttribute
        from java.util import ArrayList

        # Find feature indices
        feature_indices = []
        for req_feat in required_features:
            for i in range(instances.numAttributes() - 1):
                if instances.attribute(i).name().lower() == req_feat.lower():
                    feature_indices.append(i)
                    break
            else:
                IJ.log("ERROR: Missing feature: " + req_feat)
                return None

        # Create new dataset
        attributes = ArrayList()
        for name in required_features:
            attributes.add(WekaAttribute(name.replace(" ", "_")))

        # Copy class attribute
        class_attr = instances.classAttribute()
        attributes.add(class_attr.copy(class_attr.name()))

        new_instances = WekaInstances("FilteredData", attributes, instances.numInstances())
        new_instances.setClassIndex(new_instances.numAttributes() - 1)

        # Copy instances
        for i in range(instances.numInstances()):
            original = instances.instance(i)
            new_instance = WekaDenseInstance(new_instances.numAttributes())
            new_instance.setDataset(new_instances)

            for j, idx in enumerate(feature_indices):
                new_instance.setValue(j, original.value(idx))

            new_instance.setClassValue(original.classValue())
            new_instances.add(new_instance)

        return new_instances

    except Exception as e:
        IJ.log("ERROR: Feature extraction failed: " + str(e))
        return None

def get_available_features(instances):
    """Get list of available features from the dataset."""
    available_features = []
    for i in range(instances.numAttributes() - 1):  # Exclude class attribute
        feature_name = instances.attribute(i).name()
        available_features.append(feature_name)
    return available_features

def calculate_feature_importance(instances, feature_subset):
    """Calculate feature importance using a simple Random Forest."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        # Extract features
        filtered_instances = extract_required_features(instances, feature_subset)
        if filtered_instances is None:
            return {}

        # Train Random Forest
        rf = RandomForest()
        rf.setNumIterations(50)
        rf.setSeed(RANDOM_SEED)
        rf.buildClassifier(filtered_instances)

        # Evaluate with cross-validation
        evaluation = Evaluation(filtered_instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(rf, filtered_instances, FEATURE_SELECTION_CV_FOLDS, random)

        accuracy = evaluation.pctCorrect() / 100.0

        # Return importance score (accuracy as proxy)
        importance = {}
        for feature in feature_subset:
            importance[feature] = accuracy / len(feature_subset)  # Distribute score

        return importance

    except Exception as e:
        IJ.log("Warning: Feature importance calculation failed: " + str(e))
        return {}

def forward_feature_selection(instances, available_features, task_name):
    """Perform forward feature selection."""
    IJ.log("  Starting forward feature selection for " + task_name)

    selected_features = []
    remaining_features = available_features[:]
    best_score = 0.0

    while remaining_features and len(selected_features) < MAX_FEATURES:
        best_feature = None
        best_current_score = 0.0

        # Test adding each remaining feature
        for feature in remaining_features:
            test_features = selected_features + [feature]

            # Quick evaluation with Random Forest
            try:
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances is None:
                    continue

                score = quick_evaluate_features(filtered_instances)

                if score > best_current_score:
                    best_current_score = score
                    best_feature = feature

            except Exception as e:
                continue

        # Add best feature if it improves performance
        if best_feature and best_current_score > best_score:
            selected_features.append(best_feature)
            remaining_features.remove(best_feature)
            best_score = best_current_score
            IJ.log("    Added feature: " + best_feature + " (score: " + str(round(best_score, 3)) + ")")
        else:
            break  # No improvement found

    IJ.log("  Forward selection completed: " + str(len(selected_features)) + " features")
    return selected_features, best_score

def backward_feature_elimination(instances, available_features, task_name):
    """Perform backward feature elimination."""
    IJ.log("  Starting backward feature elimination for " + task_name)

    # Start with all available features (up to MAX_FEATURES)
    current_features = available_features[:MAX_FEATURES]
    best_score = 0.0

    # Get baseline score
    try:
        filtered_instances = extract_required_features(instances, current_features)
        if filtered_instances is not None:
            best_score = quick_evaluate_features(filtered_instances)
    except:
        best_score = 0.0

    while len(current_features) > MIN_FEATURES:
        worst_feature = None
        best_current_score = 0.0

        # Test removing each feature
        for feature in current_features:
            test_features = [f for f in current_features if f != feature]

            try:
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances is None:
                    continue

                score = quick_evaluate_features(filtered_instances)

                if score > best_current_score:
                    best_current_score = score
                    worst_feature = feature

            except Exception as e:
                continue

        # Remove worst feature if it improves performance
        if worst_feature and best_current_score > best_score:
            current_features.remove(worst_feature)
            best_score = best_current_score
            IJ.log("    Removed feature: " + worst_feature + " (score: " + str(round(best_score, 3)) + ")")
        else:
            break  # No improvement found

    IJ.log("  Backward elimination completed: " + str(len(current_features)) + " features")
    return current_features, best_score

def recursive_feature_elimination(instances, available_features, task_name):
    """Perform recursive feature elimination with cross-validation."""
    IJ.log("  Starting recursive feature elimination for " + task_name)

    # Start with all available features (up to MAX_FEATURES)
    current_features = available_features[:MAX_FEATURES]
    feature_rankings = {}

    rank = len(current_features)

    while len(current_features) > MIN_FEATURES:
        # Calculate feature importance for current set
        importance_scores = calculate_feature_importance(instances, current_features)

        if not importance_scores:
            break

        # Find least important feature
        least_important = min(importance_scores.keys(), key=lambda x: importance_scores[x])
        feature_rankings[least_important] = rank

        # Remove least important feature
        current_features.remove(least_important)
        rank -= 1

        IJ.log("    Eliminated: " + least_important + " (rank: " + str(rank + 1) + ")")

    # Assign remaining features top ranks
    for feature in current_features:
        feature_rankings[feature] = rank
        rank -= 1

    # Select top features based on ranking
    sorted_features = sorted(feature_rankings.keys(), key=lambda x: feature_rankings[x])
    selected_features = sorted_features[:min(MAX_FEATURES, len(sorted_features))]

    # Evaluate final feature set
    try:
        filtered_instances = extract_required_features(instances, selected_features)
        final_score = quick_evaluate_features(filtered_instances) if filtered_instances else 0.0
    except:
        final_score = 0.0

    IJ.log("  Recursive elimination completed: " + str(len(selected_features)) + " features")
    return selected_features, final_score

def quick_evaluate_features(instances):
    """Quick evaluation of feature set using Random Forest."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        rf = RandomForest()
        rf.setNumIterations(10)  # Faster evaluation
        rf.setSeed(RANDOM_SEED)

        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(rf, instances, FEATURE_SELECTION_CV_FOLDS, random)

        return evaluation.pctCorrect() / 100.0

    except Exception as e:
        return 0.0

def calculate_feature_correlations(instances):
    """Calculate correlation matrix for features."""
    try:
        import math

        num_features = instances.numAttributes() - 1  # Exclude class
        correlations = {}

        for i in range(num_features):
            for j in range(i + 1, num_features):
                feature1_name = instances.attribute(i).name()
                feature2_name = instances.attribute(j).name()

                # Calculate Pearson correlation
                values1 = []
                values2 = []

                for k in range(instances.numInstances()):
                    instance = instances.instance(k)
                    values1.append(instance.value(i))
                    values2.append(instance.value(j))

                correlation = calculate_pearson_correlation(values1, values2)
                correlations[(feature1_name, feature2_name)] = abs(correlation)

        return correlations

    except Exception as e:
        IJ.log("Warning: Failed to calculate correlations: " + str(e))
        return {}

def calculate_pearson_correlation(x, y):
    """Calculate Pearson correlation coefficient."""
    try:
        import math

        n = len(x)
        if n == 0:
            return 0.0

        sum_x = sum(x)
        sum_y = sum(y)
        sum_x2 = sum(xi * xi for xi in x)
        sum_y2 = sum(yi * yi for yi in y)
        sum_xy = sum(xi * yi for xi, yi in zip(x, y))

        numerator = n * sum_xy - sum_x * sum_y
        denominator = math.sqrt((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y))

        if denominator == 0:
            return 0.0

        return numerator / denominator

    except:
        return 0.0

def filter_correlated_features(instances, features, threshold=None):
    """Remove highly correlated features."""
    if threshold is None:
        threshold = FEATURE_CORRELATION_THRESHOLD

    try:
        IJ.log("  Filtering correlated features (threshold: " + str(threshold) + ")")

        correlations = calculate_feature_correlations(instances)
        features_to_remove = set()

        for (feat1, feat2), corr in correlations.items():
            if corr > threshold and feat1 in features and feat2 in features:
                # Remove the feature that appears later in the list
                if features.index(feat1) > features.index(feat2):
                    features_to_remove.add(feat1)
                else:
                    features_to_remove.add(feat2)

        filtered_features = [f for f in features if f not in features_to_remove]

        if len(features_to_remove) > 0:
            IJ.log("  Removed " + str(len(features_to_remove)) + " correlated features: " + ", ".join(features_to_remove))

        return filtered_features

    except Exception as e:
        IJ.log("  Warning: Correlation filtering failed: " + str(e))
        return features

def optimize_feature_selection(instances, task_name, baseline_features):
    """Perform comprehensive feature selection optimization with correlation filtering."""
    if not ENABLE_FEATURE_SELECTION:
        IJ.log("Feature selection disabled, using baseline features for " + task_name)
        return baseline_features, 0.0

    IJ.log("")
    IJ.log("=" * 50)
    IJ.log("FEATURE SELECTION OPTIMIZATION FOR " + task_name.upper())
    IJ.log("=" * 50)

    # Get available features from dataset
    available_features = get_available_features(instances)

    # Filter available features to only include those in our known feature pool
    valid_features = []
    for feature in available_features:
        # Case-insensitive matching
        for known_feature in ALL_AVAILABLE_FEATURES:
            if feature.lower() == known_feature.lower():
                valid_features.append(feature)
                break

    if not valid_features:
        IJ.log("ERROR: No valid features found in dataset for " + task_name)
        return baseline_features, 0.0

    IJ.log("Available features for optimization: " + str(len(valid_features)))
    IJ.log("Features: " + ", ".join(valid_features))

    # Apply correlation filtering to reduce feature space
    filtered_features = filter_correlated_features(instances, valid_features)
    IJ.log("Features after correlation filtering: " + str(len(filtered_features)))

    # Evaluate baseline performance
    baseline_score = 0.0
    try:
        baseline_instances = extract_required_features(instances, baseline_features)
        if baseline_instances:
            baseline_score = quick_evaluate_features(baseline_instances)
            IJ.log("Baseline performance (" + str(len(baseline_features)) + " features): " + str(round(baseline_score, 3)))
    except:
        pass

    # Test different feature selection methods
    best_features = baseline_features
    best_score = baseline_score
    best_method = "baseline"

    results = []

    for method in FEATURE_SELECTION_METHODS:
        try:
            if method == "forward":
                features, score = forward_feature_selection_enhanced(instances, filtered_features, task_name)
            elif method == "backward":
                features, score = backward_feature_elimination_enhanced(instances, filtered_features, task_name)
            elif method == "recursive":
                features, score = recursive_feature_elimination_enhanced(instances, filtered_features, task_name)
            else:
                continue

            results.append({
                'method': method,
                'features': features,
                'score': score,
                'num_features': len(features)
            })

            IJ.log("  " + method.capitalize() + " selection: " + str(len(features)) + " features, score: " + str(round(score, 3)))

            if score > best_score:
                best_features = features
                best_score = score
                best_method = method

        except Exception as e:
            IJ.log("  " + method.capitalize() + " selection failed: " + str(e))

    # Fine-grained feature count optimization
    if best_features != baseline_features:
        IJ.log("  Performing fine-grained feature count optimization...")
        best_features, best_score = optimize_feature_count(instances, best_features, task_name)

    # Report results
    IJ.log("")
    IJ.log("FEATURE SELECTION RESULTS FOR " + task_name.upper() + ":")
    IJ.log("Best method: " + best_method + " (score: " + str(round(best_score, 3)) + ")")
    IJ.log("Best features (" + str(len(best_features)) + "): " + ", ".join(best_features))

    if best_score > baseline_score:
        improvement = ((best_score - baseline_score) / baseline_score) * 100
        IJ.log("Improvement over baseline: +" + str(round(improvement, 1)) + "%")
    else:
        IJ.log("No improvement over baseline, using baseline features")
        best_features = baseline_features
        best_score = baseline_score

    return best_features, best_score

def forward_feature_selection_enhanced(instances, available_features, task_name):
    """Enhanced forward feature selection with fine-grained steps."""
    IJ.log("  Starting enhanced forward feature selection for " + task_name)

    selected_features = []
    remaining_features = available_features[:]
    best_score = 0.0

    while remaining_features and len(selected_features) < MAX_FEATURES:
        best_feature = None
        best_current_score = 0.0

        # Test adding each remaining feature
        for feature in remaining_features:
            test_features = selected_features + [feature]

            try:
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances is None:
                    continue

                score = quick_evaluate_features_enhanced(filtered_instances)

                if score > best_current_score:
                    best_current_score = score
                    best_feature = feature

            except Exception as e:
                continue

        # Add best feature if it improves performance
        if best_feature and best_current_score > best_score:
            selected_features.append(best_feature)
            remaining_features.remove(best_feature)
            best_score = best_current_score
            IJ.log("    Added feature: " + best_feature + " (score: " + str(round(best_score, 3)) + ")")
        else:
            break  # No improvement found

    IJ.log("  Enhanced forward selection completed: " + str(len(selected_features)) + " features")
    return selected_features, best_score

def backward_feature_elimination_enhanced(instances, available_features, task_name):
    """Enhanced backward feature elimination with fine-grained steps."""
    IJ.log("  Starting enhanced backward feature elimination for " + task_name)

    # Start with all available features (up to MAX_FEATURES)
    current_features = available_features[:MAX_FEATURES]
    best_score = 0.0

    # Get baseline score
    try:
        filtered_instances = extract_required_features(instances, current_features)
        if filtered_instances is not None:
            best_score = quick_evaluate_features_enhanced(filtered_instances)
    except:
        best_score = 0.0

    while len(current_features) > MIN_FEATURES:
        worst_feature = None
        best_current_score = 0.0

        # Test removing each feature
        for feature in current_features:
            test_features = [f for f in current_features if f != feature]

            try:
                filtered_instances = extract_required_features(instances, test_features)
                if filtered_instances is None:
                    continue

                score = quick_evaluate_features_enhanced(filtered_instances)

                if score > best_current_score:
                    best_current_score = score
                    worst_feature = feature

            except Exception as e:
                continue

        # Remove worst feature if it improves performance
        if worst_feature and best_current_score > best_score:
            current_features.remove(worst_feature)
            best_score = best_current_score
            IJ.log("    Removed feature: " + worst_feature + " (score: " + str(round(best_score, 3)) + ")")
        else:
            break  # No improvement found

    IJ.log("  Enhanced backward elimination completed: " + str(len(current_features)) + " features")
    return current_features, best_score

def recursive_feature_elimination_enhanced(instances, available_features, task_name):
    """Enhanced recursive feature elimination with importance ranking."""
    IJ.log("  Starting enhanced recursive feature elimination for " + task_name)

    # Start with all available features (up to MAX_FEATURES)
    current_features = available_features[:MAX_FEATURES]
    feature_rankings = {}

    rank = len(current_features)

    while len(current_features) > MIN_FEATURES:
        # Calculate feature importance for current set
        importance_scores = calculate_feature_importance_enhanced(instances, current_features)

        if not importance_scores:
            break

        # Find least important feature
        least_important = min(importance_scores.keys(), key=lambda x: importance_scores[x])
        feature_rankings[least_important] = rank

        # Remove least important feature
        current_features.remove(least_important)
        rank -= 1

        IJ.log("    Eliminated: " + least_important + " (rank: " + str(rank + 1) + ")")

    # Assign remaining features top ranks
    for feature in current_features:
        feature_rankings[feature] = rank
        rank -= 1

    # Select top features based on ranking
    sorted_features = sorted(feature_rankings.keys(), key=lambda x: feature_rankings[x])
    selected_features = sorted_features[:min(MAX_FEATURES, len(sorted_features))]

    # Evaluate final feature set
    try:
        filtered_instances = extract_required_features(instances, selected_features)
        final_score = quick_evaluate_features_enhanced(filtered_instances) if filtered_instances else 0.0
    except:
        final_score = 0.0

    IJ.log("  Enhanced recursive elimination completed: " + str(len(selected_features)) + " features")
    return selected_features, final_score

def optimize_feature_count(instances, features, task_name):
    """Fine-grained optimization of feature count."""
    IJ.log("  Optimizing feature count for " + task_name)

    best_features = features
    best_score = 0.0

    # Test different feature counts around the current number
    current_count = len(features)
    test_counts = range(max(MIN_FEATURES, current_count - 3),
                       min(MAX_FEATURES + 1, current_count + 4))

    for count in test_counts:
        if count == len(features):
            continue  # Skip current configuration

        if count < len(features):
            # Remove least important features
            test_features = features[:count]
        else:
            # This would require adding features, skip for now
            continue

        try:
            filtered_instances = extract_required_features(instances, test_features)
            if filtered_instances is None:
                continue

            score = quick_evaluate_features_enhanced(filtered_instances)

            if score > best_score:
                best_score = score
                best_features = test_features
                IJ.log("    Improved with " + str(count) + " features: " + str(round(score, 3)))

        except Exception as e:
            continue

    return best_features, best_score

def quick_evaluate_features_enhanced(instances):
    """Enhanced quick evaluation with better metrics."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        rf = RandomForest()
        rf.setNumIterations(25)  # Slightly more trees for better evaluation
        rf.setSeed(RANDOM_SEED)

        evaluation = Evaluation(instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(rf, instances, FEATURE_SELECTION_CV_FOLDS, random)

        # Use weighted F1 score for better evaluation
        weighted_f1 = 0.0
        total_instances = instances.numInstances()

        for i in range(instances.numClasses()):
            class_count = 0
            for j in range(instances.numInstances()):
                if int(instances.instance(j).classValue()) == i:
                    class_count += 1
            weight = class_count / float(total_instances)
            f1_score = evaluation.fMeasure(i)
            if not str(f1_score) == 'NaN':
                weighted_f1 += weight * f1_score

        return weighted_f1

    except Exception as e:
        return 0.0

def calculate_feature_importance_enhanced(instances, feature_subset):
    """Enhanced feature importance calculation."""
    try:
        from weka.classifiers.trees import RandomForest
        from weka.classifiers.evaluation import Evaluation
        from java.util import Random

        # Extract features
        filtered_instances = extract_required_features(instances, feature_subset)
        if filtered_instances is None:
            return {}

        # Train Random Forest with more trees for better importance estimation
        rf = RandomForest()
        rf.setNumIterations(100)
        rf.setSeed(RANDOM_SEED)
        rf.buildClassifier(filtered_instances)

        # Evaluate with cross-validation
        evaluation = Evaluation(filtered_instances)
        random = Random(RANDOM_SEED)
        evaluation.crossValidateModel(rf, filtered_instances, FEATURE_SELECTION_CV_FOLDS, random)

        accuracy = evaluation.pctCorrect() / 100.0

        # Calculate individual feature importance by removal
        importance = {}
        for feature in feature_subset:
            remaining_features = [f for f in feature_subset if f != feature]
            if len(remaining_features) == 0:
                importance[feature] = 0.0
                continue

            try:
                reduced_instances = extract_required_features(instances, remaining_features)
                if reduced_instances is None:
                    importance[feature] = 0.0
                    continue

                reduced_eval = Evaluation(reduced_instances)
                reduced_eval.crossValidateModel(rf, reduced_instances, FEATURE_SELECTION_CV_FOLDS, random)
                reduced_accuracy = reduced_eval.pctCorrect() / 100.0

                # Importance is the drop in accuracy when feature is removed
                importance[feature] = max(0.0, accuracy - reduced_accuracy)

            except:
                importance[feature] = 0.0

        return importance

    except Exception as e:
        IJ.log("Warning: Enhanced feature importance calculation failed: " + str(e))
        return {}

def save_best_model(classifier_name, classifier, training_data, task_name, output_dir):
    """Train and save the best model."""
    try:
        from weka.core import SerializationHelper as WekaSerializationHelper

        # Train the classifier on full dataset
        classifier.buildClassifier(training_data)

        # Generate filename
        safe_name = classifier_name.replace(" ", "_").replace("/", "_")
        model_filename = task_name.lower() + "_best_" + safe_name + ".model"
        model_path = os.path.join(output_dir, model_filename)

        # Save model
        WekaSerializationHelper.write(model_path, classifier)
        IJ.log("Best " + task_name + " model saved: " + model_path)
        return model_path

    except Exception as e:
        IJ.log("ERROR: Failed to save model: " + str(e))
        return None

def save_enhanced_results_report(porosity_results, shape_results, porosity_features, shape_features, output_dir):
    """Save comprehensive results report including feature selection results."""
    try:
        from java.io import FileWriter, BufferedWriter
        from java.io import File

        # Save detailed model performance report
        report_path = os.path.join(output_dir, "comprehensive_optimization_report.csv")
        writer = BufferedWriter(FileWriter(File(report_path)))

        # Write header
        header = "Task,Rank,Classifier,Accuracy,Weighted_F1,Num_Features,Features,Class,Precision,Recall,F1_Score\n"
        writer.write(header)

        # Write porosity results
        for rank, result in enumerate(porosity_results, 1):
            features_str = ";".join(result.get('optimal_features', porosity_features))
            num_features = result.get('num_features', len(porosity_features))

            for class_name, metrics in result['class_metrics'].items():
                row = "Porosity," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + str(result['weighted_f1']) + ","
                row += str(num_features) + ",\"" + features_str + "\","
                row += class_name + "," + str(metrics['precision']) + ","
                row += str(metrics['recall']) + "," + str(metrics['f1_score']) + "\n"
                writer.write(row)

        # Write shape results
        for rank, result in enumerate(shape_results, 1):
            features_str = ";".join(result.get('optimal_features', shape_features))
            num_features = result.get('num_features', len(shape_features))

            for class_name, metrics in result['class_metrics'].items():
                row = "Shape," + str(rank) + "," + result['classifier_name'] + ","
                row += str(result['accuracy']) + "," + str(result['weighted_f1']) + ","
                row += str(num_features) + ",\"" + features_str + "\","
                row += class_name + "," + str(metrics['precision']) + ","
                row += str(metrics['recall']) + "," + str(metrics['f1_score']) + "\n"
                writer.write(row)

        writer.close()
        IJ.log("Comprehensive report saved: " + report_path)

        # Save feature selection summary
        feature_summary_path = os.path.join(output_dir, "feature_selection_summary.csv")
        feature_writer = BufferedWriter(FileWriter(File(feature_summary_path)))

        # Write feature summary header
        feature_header = "Task,Baseline_Features,Optimal_Features,Baseline_Count,Optimal_Count,Improvement\n"
        feature_writer.write(feature_header)

        # Porosity feature summary
        baseline_porosity = ";".join(BASELINE_POROSITY_FEATURES)
        optimal_porosity = ";".join(porosity_features)
        porosity_improvement = "N/A"
        if porosity_results:
            baseline_score = porosity_results[0].get('feature_score', 0.0)
            if baseline_score > 0:
                porosity_improvement = str(round(baseline_score * 100, 1)) + "%"

        porosity_row = "Porosity,\"" + baseline_porosity + "\",\"" + optimal_porosity + "\","
        porosity_row += str(len(BASELINE_POROSITY_FEATURES)) + "," + str(len(porosity_features)) + ","
        porosity_row += porosity_improvement + "\n"
        feature_writer.write(porosity_row)

        # Shape feature summary
        baseline_shape = ";".join(BASELINE_SHAPE_FEATURES)
        optimal_shape = ";".join(shape_features)
        shape_improvement = "N/A"
        if shape_results:
            baseline_score = shape_results[0].get('feature_score', 0.0)
            if baseline_score > 0:
                shape_improvement = str(round(baseline_score * 100, 1)) + "%"

        shape_row = "Shape,\"" + baseline_shape + "\",\"" + optimal_shape + "\","
        shape_row += str(len(BASELINE_SHAPE_FEATURES)) + "," + str(len(shape_features)) + ","
        shape_row += shape_improvement + "\n"
        feature_writer.write(shape_row)

        feature_writer.close()
        IJ.log("Feature selection summary saved: " + feature_summary_path)
        return True

    except Exception as e:
        IJ.log("ERROR: Failed to save enhanced report: " + str(e))
        return False

def main():
    """Main function to run model selection and optimization."""
    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("Model Selection and Optimization")
    IJ.log("=" * 60)

    # Setup Weka
    if not setup_weka():
        return False

    # Get training data path (same file for both porosity and shape)
    training_data_path = TRAINING_DATA
    if not training_data_path or not os.path.exists(training_data_path):
        IJ.log("Training data not specified. Opening file selection...")
        training_data_path = get_training_data_path_interactive("Combined Training Data")
        if not training_data_path:
            IJ.log("ERROR: No training data selected")
            return False

    # Set output directory
    output_dir = OUTPUT_DIR
    if not output_dir:
        output_dir = os.path.dirname(training_data_path)

    # Load training data
    IJ.log("Loading training data...")
    raw_data = load_training_data(training_data_path)
    if raw_data is None:
        return False

    # Prepare separate datasets for porosity and shape classification
    IJ.log("Preparing classification datasets...")
    porosity_data = prepare_classification_data(raw_data, "Porosity")
    if porosity_data is None:
        return False

    shape_data = prepare_classification_data(raw_data, "Shape")
    if shape_data is None:
        return False

    # Find best models and optimal features for each task
    porosity_results, optimal_porosity_features = find_best_models_for_task(porosity_data, "Porosity", BASELINE_POROSITY_FEATURES)
    shape_results, optimal_shape_features = find_best_models_for_task(shape_data, "Shape", BASELINE_SHAPE_FEATURES)

    if not porosity_results or not shape_results:
        IJ.log("ERROR: Model evaluation failed")
        return False

    # Save best models
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("SAVING BEST MODELS WITH OPTIMAL FEATURES")
    IJ.log("=" * 60)

    # Get best classifiers and retrain on filtered data
    best_porosity = porosity_results[0]
    best_shape = shape_results[0]

    # Recreate and train best classifiers
    classifiers = create_classifiers()
    classifier_dict = dict(classifiers)

    if best_porosity['classifier_name'] in classifier_dict:
        porosity_filtered = extract_required_features(porosity_data, optimal_porosity_features)
        save_best_model(best_porosity['classifier_name'],
                       classifier_dict[best_porosity['classifier_name']],
                       porosity_filtered, "Porosity", output_dir)

    if best_shape['classifier_name'] in classifier_dict:
        shape_filtered = extract_required_features(shape_data, optimal_shape_features)
        save_best_model(best_shape['classifier_name'],
                       classifier_dict[best_shape['classifier_name']],
                       shape_filtered, "Shape", output_dir)

    # Save comprehensive reports
    save_enhanced_results_report(porosity_results, shape_results, optimal_porosity_features, optimal_shape_features, output_dir)

    # Display comprehensive final recommendations
    IJ.log("")
    IJ.log("=" * 60)
    IJ.log("COMPREHENSIVE OPTIMIZATION RESULTS")
    IJ.log("=" * 60)

    IJ.log("POROSITY CLASSIFICATION:")
    IJ.log("  Best Model: " + best_porosity['classifier_name'])
    IJ.log("  Accuracy: " + str(round(best_porosity.get('accuracy', 0.0), 3)))
    IJ.log("  Weighted F1: " + str(round(best_porosity.get('weighted_f1', 0.0), 3)))

    # Additional metrics if available
    if 'weighted_precision' in best_porosity:
        IJ.log("  Weighted Precision: " + str(round(best_porosity['weighted_precision'], 3)))
        IJ.log("  Weighted Recall: " + str(round(best_porosity['weighted_recall'], 3)))

    if 'kappa' in best_porosity:
        IJ.log("  Cohen's Kappa: " + str(round(best_porosity['kappa'], 3)))

    if 'accuracy_std' in best_porosity:
        IJ.log("  Accuracy Std Dev: " + str(round(best_porosity['accuracy_std'], 3)))

    if 'statistical_significance' in best_porosity:
        IJ.log("  Statistical Significance: " + best_porosity['statistical_significance'])

    IJ.log("  Optimal Features (" + str(len(optimal_porosity_features)) + "): " + ", ".join(optimal_porosity_features))

    if len(optimal_porosity_features) != len(BASELINE_POROSITY_FEATURES):
        improvement = "improvement" if len(optimal_porosity_features) > len(BASELINE_POROSITY_FEATURES) else "reduction"
        IJ.log("  Feature " + improvement + ": " + str(len(BASELINE_POROSITY_FEATURES)) + " -> " + str(len(optimal_porosity_features)) + " features")

    IJ.log("")
    IJ.log("SHAPE CLASSIFICATION:")
    IJ.log("  Best Model: " + best_shape['classifier_name'])
    IJ.log("  Accuracy: " + str(round(best_shape.get('accuracy', 0.0), 3)))
    IJ.log("  Weighted F1: " + str(round(best_shape.get('weighted_f1', 0.0), 3)))

    # Additional metrics if available
    if 'weighted_precision' in best_shape:
        IJ.log("  Weighted Precision: " + str(round(best_shape['weighted_precision'], 3)))
        IJ.log("  Weighted Recall: " + str(round(best_shape['weighted_recall'], 3)))

    if 'kappa' in best_shape:
        IJ.log("  Cohen's Kappa: " + str(round(best_shape['kappa'], 3)))

    if 'accuracy_std' in best_shape:
        IJ.log("  Accuracy Std Dev: " + str(round(best_shape['accuracy_std'], 3)))

    if 'statistical_significance' in best_shape:
        IJ.log("  Statistical Significance: " + best_shape['statistical_significance'])

    IJ.log("  Optimal Features (" + str(len(optimal_shape_features)) + "): " + ", ".join(optimal_shape_features))

    if len(optimal_shape_features) != len(BASELINE_SHAPE_FEATURES):
        improvement = "improvement" if len(optimal_shape_features) > len(BASELINE_SHAPE_FEATURES) else "reduction"
        IJ.log("  Feature " + improvement + ": " + str(len(BASELINE_SHAPE_FEATURES)) + " -> " + str(len(optimal_shape_features)) + " features")

    # Optimization summary
    IJ.log("")
    IJ.log("OPTIMIZATION SUMMARY:")
    if ENABLE_FEATURE_SELECTION:
        IJ.log("  Feature Selection: ENABLED")
    if ENABLE_MULTI_STAGE_OPTIMIZATION:
        IJ.log("  Multi-stage Optimization: ENABLED")
    if ENABLE_NESTED_CV:
        IJ.log("  Nested Cross-Validation: ENABLED")
    if ENABLE_STATISTICAL_TESTING:
        IJ.log("  Statistical Testing: ENABLED")
    if ENABLE_DETAILED_METRICS:
        IJ.log("  Detailed Metrics: ENABLED")

    IJ.log("")
    IJ.log("INTEGRATION NOTES:")
    IJ.log("To use these optimized configurations in your ImageJ/FIJI scripts:")
    IJ.log("1. Update POROSITY_REQUIRED_FEATURES = " + str(optimal_porosity_features))
    IJ.log("2. Update SHAPE_REQUIRED_FEATURES = " + str(optimal_shape_features))
    IJ.log("3. Use the saved .model files with optimal configurations")
    IJ.log("4. Test the new configuration on your particle analysis pipeline")
    IJ.log("5. Monitor performance improvements in production use")

    return True

# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Model selection completed successfully!")
        else:
            IJ.log("Model selection failed!")
    except Exception as e:
        IJ.log("ERROR: " + str(e))
        import traceback
        traceback.print_exc()
