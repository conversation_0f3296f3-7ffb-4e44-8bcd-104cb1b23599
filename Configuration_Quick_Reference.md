# Configuration Quick Reference

## Enhanced find_best_models.py Configuration Options

### **Core Settings**
```python
# Training data path
TRAINING_DATA = "path/to/your/training_data.arff"

# Output directory (optional)
OUTPUT_DIR = ""  # Uses same directory as training data if empty
```

### **Feature Selection Configuration**
```python
# Enable/disable feature selection optimization
ENABLE_FEATURE_SELECTION = True

# Feature selection methods to test
FEATURE_SELECTION_METHODS = [
    "forward",     # Forward feature selection
    "backward",    # Backward elimination
    "recursive"    # Recursive feature elimination
]

# Feature constraints
MIN_FEATURES = 1                        # Minimum features to test
MAX_FEATURES = 15                       # Maximum features to test
FEATURE_CORRELATION_THRESHOLD = 0.95    # Remove correlated features
FEATURE_SELECTION_CV_FOLDS = 5          # CV folds for feature selection
```

### **Multi-Stage Optimization**
```python
# Enable two-phase optimization
ENABLE_MULTI_STAGE_OPTIMIZATION = True

# Number of top models for detailed optimization
TOP_MODELS_FOR_DETAILED_OPTIMIZATION = 5

# Hyperparameter optimization method
HYPERPARAMETER_OPTIMIZATION_METHOD = "grid"  # "grid" or "random"

# Random search iterations (if using random method)
RANDOM_SEARCH_ITERATIONS = 50
```

### **Cross-Validation Settings**
```python
# Enable nested cross-validation
ENABLE_NESTED_CV = True

# Cross-validation folds
OUTER_CV_FOLDS = 5      # Outer CV for model evaluation
INNER_CV_FOLDS = 3      # Inner CV for hyperparameter optimization
CV_FOLDS = 10           # Standard CV folds
```

### **Statistical Testing**
```python
# Enable statistical significance testing
ENABLE_STATISTICAL_TESTING = True

# Statistical parameters
SIGNIFICANCE_LEVEL = 0.05              # Alpha level for tests
STATISTICAL_TEST_ITERATIONS = 100      # Bootstrap iterations
```

### **Performance Metrics**
```python
# Enable comprehensive metrics
ENABLE_DETAILED_METRICS = True

# Save additional analysis files
SAVE_CONFUSION_MATRICES = True
SAVE_ROC_CURVES = True
SAVE_PR_CURVES = True
```

### **Baseline Feature Sets**
```python
# Current baseline features (automatically detected from ARFF)
BASELINE_POROSITY_FEATURES = ["total_black_pixels"]
BASELINE_SHAPE_FEATURES = ["Area", "Solidity", "Convexity", "Roundness", "Feret_Ratio"]
```

## Performance Modes

### **Fast Mode (Quick Testing)**
```python
ENABLE_FEATURE_SELECTION = True
ENABLE_MULTI_STAGE_OPTIMIZATION = False
ENABLE_NESTED_CV = False
ENABLE_STATISTICAL_TESTING = False
MAX_FEATURES = 8
TOP_MODELS_FOR_DETAILED_OPTIMIZATION = 3
```

### **Balanced Mode (Recommended)**
```python
ENABLE_FEATURE_SELECTION = True
ENABLE_MULTI_STAGE_OPTIMIZATION = True
ENABLE_NESTED_CV = True
ENABLE_STATISTICAL_TESTING = False
MAX_FEATURES = 12
TOP_MODELS_FOR_DETAILED_OPTIMIZATION = 5
```

### **Thorough Mode (Maximum Quality)**
```python
ENABLE_FEATURE_SELECTION = True
ENABLE_MULTI_STAGE_OPTIMIZATION = True
ENABLE_NESTED_CV = True
ENABLE_STATISTICAL_TESTING = True
ENABLE_DETAILED_METRICS = True
MAX_FEATURES = 15
TOP_MODELS_FOR_DETAILED_OPTIMIZATION = 5
```

### **Feature Selection Only**
```python
ENABLE_FEATURE_SELECTION = True
ENABLE_MULTI_STAGE_OPTIMIZATION = False
ENABLE_NESTED_CV = False
ENABLE_STATISTICAL_TESTING = False
# Keep other settings as default
```

## Expected Runtime

### **Dataset Size Impact**
- **Small dataset** (<100 instances): 30-60 minutes (thorough mode)
- **Medium dataset** (100-500 instances): 1-3 hours (thorough mode)
- **Large dataset** (>500 instances): 3-6 hours (thorough mode)

### **Configuration Impact**
- **Fast mode**: ~25% of thorough mode time
- **Balanced mode**: ~60% of thorough mode time
- **Feature selection only**: ~40% of thorough mode time

## Output Files

### **Generated Reports**
1. `comprehensive_optimization_report.csv` - Detailed model performance
2. `feature_selection_summary.csv` - Feature optimization results
3. `porosity_best_[ModelName].model` - Best porosity model
4. `shape_best_[ModelName].model` - Best shape model

### **Console Output Sections**
1. **Feature Selection Optimization** - Feature selection results
2. **Phase 1: Broad Classifier Screening** - Initial model testing
3. **Phase 2: Detailed Hyperparameter Optimization** - Fine-tuning results
4. **Phase 3: Statistical Significance Testing** - Statistical validation
5. **Comprehensive Optimization Results** - Final recommendations

## Troubleshooting

### **Long Runtime**
- Reduce `MAX_FEATURES` to 10 or less
- Disable `ENABLE_MULTI_STAGE_OPTIMIZATION`
- Disable `ENABLE_STATISTICAL_TESTING`
- Use Fast Mode configuration

### **Memory Issues**
- Reduce `CV_FOLDS` to 5
- Reduce `OUTER_CV_FOLDS` to 3
- Disable `ENABLE_NESTED_CV`

### **No Improvement Found**
- Increase `MAX_FEATURES` to 15
- Enable all optimization features
- Check that training data has sufficient features
- Verify feature names match available data

### **Statistical Tests Failing**
- Increase `STATISTICAL_TEST_ITERATIONS`
- Check that models have sufficient performance differences
- Verify adequate training data size

## Integration with ImageJ/FIJI

### **Using Optimized Results**
1. **Copy optimal feature lists** from console output
2. **Update batch processing scripts** with new feature configurations
3. **Replace model files** with optimized .model files
4. **Test on sample data** before full deployment
5. **Monitor performance** in production use

### **Feature Configuration Update**
```python
# In your batch processing scripts, update:
POROSITY_REQUIRED_FEATURES = ["optimal_features_from_report"]
SHAPE_REQUIRED_FEATURES = ["optimal_features_from_report"]
```

### **Model File Usage**
- Use the generated `.model` files with the corresponding optimal feature sets
- Ensure feature extraction in ImageJ matches the optimized feature lists
- Test classification performance on known samples

## Best Practices

1. **Start with Balanced Mode** for initial optimization
2. **Use Thorough Mode** for final production models
3. **Validate results** on independent test data
4. **Document configurations** used for reproducibility
5. **Monitor performance** after deployment
6. **Re-optimize periodically** as data grows

This configuration reference provides quick access to all enhancement options for optimal model selection and feature optimization.
