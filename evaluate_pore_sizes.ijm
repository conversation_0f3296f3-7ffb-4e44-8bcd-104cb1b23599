args = getArgument();
argArray = split(args, ",");
pixels = 214;
microns = 50;

run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");
run("Set Scale...", "distance="+pixels+" known="+microns+" unit=µm global");
run("Set Measurements...", "area perimeter area_fraction fit shape feret's");
run("Analyze Particles...", "size=100-infinity minimum=50 show=Overlay add display exclude include clear record");

n = nResults;
	
deleted_entries = 0;

// excluding the scale bar
for (i=n-1; i>=0; i--) {
    if ((getResult('XStart', i)>2260) && (getResult('YStart', i)>1565))	{
		roiManager("select", i);
		roiManager("Delete");
		deleted_entries++;
	}
}
n = n-deleted_entries;

// =============================================================================
// SMART BINNING FUNCTION
// =============================================================================
function calculateOptimalBins(dataArray, minVal, maxVal, sampleSize) {
    // Multiple methods to determine optimal bin count

    // Method 1: Sturges' Rule - good for normal distributions
    sturgesBins = round(1 + log(sampleSize) / log(2) + 0.5);  // Manual ceiling

    // Method 2: Scott's Rule - based on data variability
    Array.getStatistics(dataArray, min, max, mean, stdDev);
    if (stdDev > 0 && sampleSize > 1) {
        binWidth = 3.5 * stdDev / pow(sampleSize, 1.0/3.0);
        scottBins = round((maxVal - minVal) / binWidth + 0.5);  // Manual ceiling
    } else {
        scottBins = sturgesBins;
    }

    // Method 3: Freedman-Diaconis Rule - robust to outliers
    // Calculate IQR (Interquartile Range)
    sortedData = Array.copy(dataArray);
    Array.sort(sortedData);
    q1Index = round(sampleSize * 0.25);
    q3Index = round(sampleSize * 0.75);
    if (q1Index >= sampleSize) q1Index = sampleSize - 1;
    if (q3Index >= sampleSize) q3Index = sampleSize - 1;
    if (q1Index < 0) q1Index = 0;
    if (q3Index < 0) q3Index = 0;

    q1 = sortedData[q1Index];
    q3 = sortedData[q3Index];
    iqr = q3 - q1;

    if (iqr > 0 && sampleSize > 1) {
        fdBinWidth = 2 * iqr / pow(sampleSize, 1.0/3.0);
        fdBins = round((maxVal - minVal) / fdBinWidth + 0.5);  // Manual ceiling
    } else {
        fdBins = sturgesBins;
    }

    // Method 4: Square Root Rule - simple and often effective
    sqrtBins = round(sqrt(sampleSize) + 0.5);  // Manual ceiling

    // Apply constraints and choose optimal method
    minBins = 5;   // Minimum for meaningful distribution
    maxBins = 50;  // Maximum for visual clarity

    // Constrain all methods
    sturgesBins = maxOf(minBins, minOf(maxBins, sturgesBins));
    scottBins = maxOf(minBins, minOf(maxBins, scottBins));
    fdBins = maxOf(minBins, minOf(maxBins, fdBins));
    sqrtBins = maxOf(minBins, minOf(maxBins, sqrtBins));

    // Choose the best method based on sample size and data characteristics
    if (sampleSize < 20) {
        // Small samples: use conservative approach
        optimalBins = minOf(sturgesBins, sqrtBins);
    } else if (sampleSize < 100) {
        // Medium samples: prefer Sturges or Square Root
        optimalBins = round((sturgesBins + sqrtBins) / 2);
    } else {
        // Large samples: use more sophisticated methods
        // Average of Scott's and Freedman-Diaconis, but prefer FD for robustness
        if (abs(fdBins - scottBins) < 5) {
            optimalBins = round((fdBins + scottBins) / 2);
        } else {
            optimalBins = fdBins;  // More robust to outliers
        }
    }

    // Final constraint check
    optimalBins = maxOf(minBins, minOf(maxBins, optimalBins));

    // Ensure we don't have more bins than unique values
    uniqueValues = countUniqueValues(dataArray);
    if (optimalBins > uniqueValues) {
        optimalBins = maxOf(minBins, uniqueValues);
    }

    return optimalBins;
}

function countUniqueValues(dataArray) {
    if (dataArray.length == 0) return 0;

    sortedData = Array.copy(dataArray);
    Array.sort(sortedData);

    uniqueCount = 1;
    for (i = 1; i < sortedData.length; i++) {
        if (sortedData[i] != sortedData[i-1]) {
            uniqueCount++;
        }
    }
    return uniqueCount;
}

// =============================================================================
// MAIN ANALYSIS
// =============================================================================

// Arrays to store all pore data for final distribution
allPoreAreas = newArray(0);
allPoreParticleIDs = newArray(0);
totalPoreCount = 0;

// Get original image for pore extraction
originalID = getImageID();
originalTitle = getTitle();

print("\\Clear");
print("=== PORE SIZE EXTRACTION ANALYSIS ===");
print("Image: " + originalTitle);
print("Found " + n + " particles for pore analysis");
print("");

// Process each particle to extract pores
for (i=0; i<n; i++) {
    print("Processing particle " + (i+1) + " of " + n + "...");

    // Select the particle ROI from the original image
    selectImage(originalID);
    roiManager("select", i);

    // Create a working image for this particle
    run("Duplicate...", "title=Particle_" + (i+1));
    particleID = getImageID();

    // Clear outside the particle to isolate it
    run("Clear Outside");

    // Invert the image so pores (holes) become white particles
    run("Invert");

    // Analyze pores within this particle (don't add to ROI manager)
    run("Set Measurements...", "area centroid redirect=None decimal=3");
    run("Analyze Particles...", "size=1-Infinity display exclude clear");

    nPores = nResults;

    if (nPores > 0) {
        print("  Found " + nPores + " pores in particle " + (i+1));

        // Store pore data for this particle
        for (j = 0; j < nPores; j++) {
            poreArea = getResult("Area", j);

            // Expand arrays to store new pore data
            allPoreAreas = Array.concat(allPoreAreas, poreArea);
            allPoreParticleIDs = Array.concat(allPoreParticleIDs, i+1);
            totalPoreCount++;

            print("    Pore " + (j+1) + ": Area = " + d2s(poreArea, 3) + " µm²");
        }
    } else {
        print("  No pores found in particle " + (i+1));
    }

    // Clean up - close the working image and clear results
    selectImage(particleID);
    close();
    run("Clear Results");
    
}

print("");
print("=== PORE SIZE DISTRIBUTION RESULTS ===");
print("Total particles analyzed: " + n);
print("Total pores found: " + totalPoreCount);

if (totalPoreCount > 0) {
    // Calculate statistics
    Array.getStatistics(allPoreAreas, min, max, mean, stdDev);

    print("Pore area statistics:");
    print("  Minimum: " + d2s(min, 3) + " µm²");
    print("  Maximum: " + d2s(max, 3) + " µm²");
    print("  Mean: " + d2s(mean, 3) + " µm²");
    print("  Std Dev: " + d2s(stdDev, 3) + " µm²");

    // Create detailed results table
    run("Clear Results");
    for (i = 0; i < totalPoreCount; i++) {
        setResult("Pore_ID", i, i+1);
        setResult("Particle_ID", i, allPoreParticleIDs[i]);
        setResult("Pore_Area_um2", i, allPoreAreas[i]);
    }
    updateResults();

    // Save results
    saveDir = getDirectory("image");
    if (saveDir == "") {
        saveDir = getDirectory("Choose directory to save results");
    }

    baseName = File.nameWithoutExtension;
    if (baseName == "") {
        baseName = "PoreAnalysis";
    }

    // Save detailed pore data
    saveAs("Results", saveDir + baseName + "_PoreData.csv");

    // Create and save pore size distribution histogram with smart binning
    optimalBins = calculateOptimalBins(allPoreAreas, min, max, totalPoreCount);
    print("  Using " + optimalBins + " bins for histogram (optimized for " + totalPoreCount + " pores)");

    Plot.create("Pore Size Distribution", "Pore Area (µm²)", "Frequency");
    Plot.addHistogram(allPoreAreas, optimalBins);
    Plot.show();

    // Save histogram
    selectWindow("Pore Size Distribution");
    saveAs("PNG", saveDir + baseName + "_PoreSizeDistribution.png");

    print("");
    print("Results saved:");
    print("  Detailed data: " + baseName + "_PoreData.csv");
    print("  Distribution plot: " + baseName + "_PoreSizeDistribution.png");

} else {
    print("No pores found in any particles.");
}

print("");
print("=== ANALYSIS COMPLETE ===");